<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UpdateMemberPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Find the member role
        $memberRole = Role::where('name', 'member')->first();
        
        if (!$memberRole) {
            $this->command->info('Member role not found. Creating it...');
            $memberRole = Role::create(['name' => 'member', 'guard_name' => 'web']);
        }

        // Define the permissions that members should have
        $memberPermissions = [
            // Project permissions
            'create_projects',
            'manage_projects',
            'edit_projects',
            
            // Task permissions
            'create_tasks',
            'manage_tasks',
            'edit_tasks',
            
            // Client permissions
            'create_clients',
            'manage_clients',
            'edit_clients',
            
            // Workspace permissions
            'manage_workspaces',
            
            // Status permissions
            'create_statuses',
            'manage_statuses',
            'edit_statuses',
            
            // Priority permissions
            'create_priorities',
            'manage_priorities',
            'edit_priorities',
            
            // Tag permissions
            'create_tags',
            'manage_tags',
            'edit_tags',
        ];

        // Create permissions if they don't exist and assign to member role
        $permissionIds = [];
        foreach ($memberPermissions as $permissionName) {
            $permission = Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web'
            ]);
            $permissionIds[] = $permission->id;
        }

        // Sync permissions to member role
        $memberRole->permissions()->sync($permissionIds);
        
        $this->command->info('Member role permissions updated successfully.');
        
        // Also update any existing users with member role to ensure they have the permissions
        $memberUsers = \App\Models\User::role('member')->get();
        foreach ($memberUsers as $user) {
            $user->syncPermissions($memberPermissions);
        }
        
        $this->command->info('Updated permissions for ' . $memberUsers->count() . ' member users.');
    }
}
