'use strict';

// Date picker initialization for Project Start and End Dates
$(document).ready(function () {
    // Initialize date picker for Start Date
    if ($('#start_date').length) {
        $('#start_date').daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            autoUpdateInput: true,
            minDate: moment().format('YYYY-MM-DD'), // From today onwards
            locale: {
                format: js_date_format || 'YYYY-MM-DD',
                cancelLabel: 'Clear'
            }
        });

        // Handle clear functionality
        $('#start_date').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });

        // Update end date minimum when start date changes
        $('#start_date').on('apply.daterangepicker', function(ev, picker) {
            var startDate = picker.startDate.format('YYYY-MM-DD');
            if ($('#end_date').length && $('#end_date').data('daterangepicker')) {
                $('#end_date').data('daterangepicker').setMinDate(startDate);
            }
        });
    }

    // Initialize date picker for End Date
    if ($('#end_date').length) {
        var minEndDate = $('#start_date').val() || moment().format('YYYY-MM-DD');
        
        $('#end_date').daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            autoUpdateInput: true,
            minDate: minEndDate, // From start date or today onwards
            locale: {
                format: js_date_format || 'YYYY-MM-DD',
                cancelLabel: 'Clear'
            }
        });

        // Handle clear functionality
        $('#end_date').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });
    }
});
