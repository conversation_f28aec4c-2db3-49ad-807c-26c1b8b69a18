<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class FixMemberPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:member-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix member role permissions to allow project and client creation';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Fixing member role permissions...');

        // Find the member role
        $memberRole = Role::where('name', 'member')->first();
        
        if (!$memberRole) {
            $this->error('Member role not found. Creating it...');
            $memberRole = Role::create(['name' => 'member', 'guard_name' => 'web']);
        }

        // Define the permissions that members should have
        $memberPermissions = [
            // Project permissions
            'create_projects',
            'manage_projects',
            'edit_projects',
            
            // Task permissions
            'create_tasks',
            'manage_tasks',
            'edit_tasks',
            
            // Client permissions
            'create_clients',
            'manage_clients',
            'edit_clients',
            
            // Workspace permissions
            'manage_workspaces',
            
            // Status permissions
            'create_statuses',
            'manage_statuses',
            'edit_statuses',
            
            // Priority permissions
            'create_priorities',
            'manage_priorities',
            'edit_priorities',
            
            // Tag permissions
            'create_tags',
            'manage_tags',
            'edit_tags',
        ];

        // Create permissions if they don't exist and assign to member role
        $permissionIds = [];
        foreach ($memberPermissions as $permissionName) {
            $permission = Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web'
            ]);
            $permissionIds[] = $permission->id;
            $this->info("Permission '{$permissionName}' ensured.");
        }

        // Sync permissions to member role
        $memberRole->permissions()->sync($permissionIds);
        
        $this->info('Member role permissions updated successfully.');
        
        // Also update any existing users with member role to ensure they have the permissions
        $memberUsers = User::role('member')->get();
        foreach ($memberUsers as $user) {
            $user->syncPermissions($memberPermissions);
        }
        
        $this->info('Updated permissions for ' . $memberUsers->count() . ' member users.');
        
        // Clear cache
        \Artisan::call('cache:clear');
        
        $this->info('Cache cleared. Member permissions fix completed!');
        
        return 0;
    }
}
